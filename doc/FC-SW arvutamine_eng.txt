DATA
You can use the freely available LEMON database as the data source. EEG recordings can be found here:
https://fcon_1000.projects.nitrc.org/indi/retro/MPI_LEMON.html
From there, select EEG Download Page → Preprocessed Data.
Initially, I recommend using data from just one subject.
The sampling rate of these EEG signals is 2500 Hz.

PREPROCESSING
You can use Python or MATLAB. We mostly use MATLAB because it has a stronger signal processing toolbox. However, most things can also be done in Python, and I have previously used Python as well.

1. Load the data

2. Find the correct segments from the signal
The data includes "events," and we are interested in event S210. The segment of interest starts at the moment marked as S210 and lasts for 1 minute. During the recording of these segments, the subjects sat with their eyes closed and did not perform any tasks—they simply sat still.

3. You may reduce the sampling rate (for example, to 500 Hz). This is not strictly necessary, but it speeds up computation. Many signal processing methods can be time-consuming, and in the initial phase, it is reasonable to save time this way.

4. Split the obtained segments into smaller ones (e.g., 10 seconds). This step also helps speed up computation.

PROCESSING
5. Compute magnitude-squared coherence (MSC) for each pair of channels. When computing MSC, use only the frequency range 8–12 Hz, which corresponds to the alpha frequency. This should result in a 62x62 matrix for each segment.
6. Calculate the average value of all elements in a matrix and the median value across all segments. This should result in a single number.
7. For each segment: take the matrices obtained in step 5 → keep only the top 40% strongest connections → convert the remaining values to binary and calculate small-worldness. In Python, you can use packages like networkx or bctpy. If you generate random networks, make sure to use the Erdős–Rényi model.
8. Once you have a small-worldness value for each segment, you can calculate the median value across all segments.