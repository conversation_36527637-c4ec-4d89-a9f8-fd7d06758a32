fs = 1000;                 % Sampling frequency (Hz)
duration = 5; % in seconds
t = 0 : 1/fs : duration - 1/fs;         % Time vector (1 second duration)
f_x = 3;
f_y = 5;
x = sin(2*pi*f_x*t);
y = 0.5 * sin(2*pi*f_y*t + pi/4);
z = x + y;

%plot(t, x, 'b', t, y, 'r', t, z, 'g');
%plot(t, z, 'g');

X = fft(z);                  % Compute the FFT

n = length(z);               % Number of samples
frequencies = (0:n-1) / duration;       % Frequency vector (0 to fs)

[~, idx_x] = min(abs(frequencies - 3));
[~, idx_y] = min(abs(frequencies - 5));

%disp(angle(X(idx_x))); % -1.5708  -1/2*pi
%disp(angle(X(idx_y))); % -0.7854  -1/4*pi

magnitude = abs(X)/n;       % Normalize the amplitude
plot(frequencies, magnitude); xlim([0 50]);
xlabel('Frequency (Hz)');
ylabel('Magnitude');

