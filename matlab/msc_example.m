fs = 500;             % Sampling frequency in Hz
t = 0 : 1/fs : 10;    % 10 seconds of data
f0 = 10;

x = sin(2*pi*f0*t);   % First sine wave
y = sin(2*pi*f0*t + pi/8) + 0.2*randn(size(t));   %   Second sine wave, same frequency, same phase
% y = sin(2*pi*f*t);

% plot(t, x, 'b', t, y, 'r');

win = hamming(500);     % 1-second window
nfft = 1024;
[msc, f] = mscohere(x, y, win, 250, nfft, fs);
%[msc, f] = mscohere(x, y, [], [], [], fs);

%disp(length(x)); % 501
%disp(length(msc)); % 129

plot(f, msc); xlim([0 30]); ylim([0 1.05]);


%xlabel('Frequency (Hz)');
%ylabel('Magnitude-Squared Coherence');
%title('MSC between x and y');
%grid on;
%xlim([0 50]);  % Zoom in to 0–50 Hz