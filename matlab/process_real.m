load('../data/channel_03.mat');

z = dataSegment;

fs = 2500;

X = fft(z);                  % Compute the FFT

n = length(z);               % Number of samples
frequencies = (0:n-1) * (fs/n);       % Frequency vector (0 to fs)

magnitude = abs(X)/n;       % Normalize the amplitude


t = 0 : 1/fs : 10-1/fs;    % 10 seconds of data
f_x = 7.4;
x = sin(2 * pi * f_x * t);

disp(length(t));
disp(length(z));
disp(length(x));

%plot(t, z, 'b', t, x, 'r');

plot(frequencies, magnitude, 'b'); xlim([0 70]);

