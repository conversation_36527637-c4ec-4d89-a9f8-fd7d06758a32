% EEG = pop_loadset('sub-032344/sub-032344_EC.set');
EEG = pop_loadbv('/mnt/4tb/downloadable/sub-032390/', 'sub-032390.vhdr');

eventTypes = {EEG.event.type};

%disp(unique(eventTypes)); % {'S  1'}    {'S200'}    {'S210'}
%disp(EEG.srate); % 2500
%disp(EEG.nbchan); % 62

channelIdx = 31; % 1=Fp1, 3=F7, 15=C4, 31=O2

% Find all events of type 'S210'
eventIndices = find(strcmp({EEG.event.type}, 'S210'));

% Pick the first occurrence (or loop over all if needed)
firstEvent = EEG.event(eventIndices(10));
startSample = firstEvent.latency + EEG.srate * 10;  % latency is the sample index

endSample = startSample + EEG.srate * 20 - 1;  % 10 second later

dataSegment = EEG.data(channelIdx, startSample:endSample);

%disp(startSample);
%disp(endSample);
disp(length(dataSegment));

%plot(dataSegment);

fs_new = 500;
fs_old = 2500;

[p, q] = rat(fs_new / fs_old);
dataResampled = resample(dataSegment, p, q);
disp(length(dataResampled));

save('../data/channel_03.mat', 'dataSegment');
