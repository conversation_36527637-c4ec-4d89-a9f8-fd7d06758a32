EEG = pop_loadset('/mnt/4tb/downloadable/sub-032302/sub-032302_EC.set');

disp(EEG.srate);
disp(EEG.nbchan);

z = EEG.data(2, 250*20:250*30);

% plot(EEG.data(1, 1:250*10));

fs = 250;

X = fft(z);                  % Compute the FFT

n = length(z);               % Number of samples
frequencies = (0:n-1) * (fs/n);       % Frequency vector (0 to fs)

magnitude = abs(X)/n;       % Normalize the amplitude

plot(frequencies, magnitude, 'b'); xlim([0 70]);
