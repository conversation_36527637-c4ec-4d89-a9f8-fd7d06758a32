
path = 'sub-032301.vmrk'

def parse_stimulus_events(file_path):
    events = []

    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()

                # Look for lines containing "Mk5=Stimulus," pattern
                if "Mk5=Stimulus," in line:
                    try:
                        # Split the line by commas to extract components
                        parts = line.split(',')

                        # Find the index of the part containing "Mk5=Stimulus"
                        stimulus_index = -1
                        for i, part in enumerate(parts):
                            if "Mk5=Stimulus" in part:
                                stimulus_index = i
                                break

                        if stimulus_index != -1 and len(parts) > stimulus_index + 2:
                            # Extract event ID (next part after "Mk5=Stimulus")
                            event_id = parts[stimulus_index + 1].strip()

                            # Extract start position (part after event ID)
                            start_position_str = parts[stimulus_index + 2].strip()
                            start_position = int(start_position_str)

                            # Store the extracted data
                            event_data = {
                                'event_id': event_id,
                                'start_position': start_position
                            }
                            events.append(event_data)

                    except (ValueError, IndexError) as e:
                        print(f"Warning: Could not parse line {line_num}: {line}")
                        print(f"Error: {e}")
                        continue

    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return []
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return []

    return events

events = parse_stimulus_events(path)

print(events)


